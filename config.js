// Restaurant Configuration - Central Variable System
// All changeable content is controlled through these variables

window.RestaurantConfig = {
    // Restaurant Information
    restaurant: {
        nameEn: 'Al-Andalus Restaurant',
        nameAr: 'مطعم الأندلس',
        phone: '+966 12 534 0000',
        addressEn: 'Ibrahim <PERSON>halil Street, Al Haram, Makkah 21955, Saudi Arabia',
        addressAr: 'شارع إبراهيم الخليل، الحرم، مكة 21955، المملكة العربية السعودية'
    },

    // Menu Items - All categories
    menu: {
        appetizers: [
            {
                nameEn: "Hummus",
                nameAr: "حمص",
                descEn: "Creamy chickpea dip with tahini and olive oil",
                descAr: "حمص كريمي مع طحينة وزيت زيتون",
                price: "25",
                image: "images/hummus.jpg"
            },
            {
                nameEn: "<PERSON>",
                nameAr: "بابا غنوج",
                descEn: "Smoky eggplant dip with tahini and garlic",
                descAr: "متبل باذنجان مدخن مع طحينة وثوم",
                price: "24",
                image: "images/baba-ghanoush.jpg"
            },
            {
                nameEn: "Muhammara",
                nameAr: "محمرة",
                descEn: "Spicy red pepper and walnut dip",
                descAr: "متبل الفلفل الأحمر الحار والجوز",
                price: "26",
                image: "images/muhammara.jpg"
            },
            {
                nameEn: "Kibbeh",
                nameAr: "كبة",
                descEn: "Fried bulgur shells stuffed with spiced meat",
                descAr: "أقراص البرغل المقلية المحشوة باللحم المتبل",
                price: "30",
                image: "images/kibbeh.jpg"
            },
            {
                nameEn: "Warak Enab",
                nameAr: "ورق عنب",
                descEn: "Grape leaves stuffed with rice and herbs",
                descAr: "أوراق عنب محشوة بالأرز والأعشاب",
                price: "22",
                image: "images/warak-enab.jpg"
            },
            {
                nameEn: "Makdous",
                nameAr: "مكدوس",
                descEn: "Pickled eggplants stuffed with walnuts and garlic",
                descAr: "باذنجان مخلل محشو بالجوز والثوم",
                price: "24",
                image: "images/makdous.jpg"
            },
            {
                nameEn: "Cheese Sambousek",
                nameAr: "سمبوسك بالجبنة",
                descEn: "Fried pastry stuffed with white cheese",
                descAr: "معجنات مقلية محشوة بالجبن الأبيض",
                price: "32",
                image: "images/cheese-sambousek.jpg"
            },
            {
                nameEn: "Labneh",
                nameAr: "لبنة",
                descEn: "Strained yogurt with olive oil and za'atar",
                descAr: "لبن مصفى مع زيت زيتون وزعتر",
                price: "18",
                image: "images/labneh.jpg"
            },
            {
                nameEn: "French Fries",
                nameAr: "بطاطس مقلية",
                descEn: "Crispy french fries",
                descAr: "بطاطس مقلية مقرمشة",
                price: "18",
                image: "images/french-fries.jpg"
            }
        ],
        main: [
            {
                nameEn: "Beef Kebab",
                nameAr: "كباب لحم",
                descEn: "Grilled minced beef skewers with spices",
                descAr: "أسياخ لحم بقري مفروم مشوي مع التوابل",
                price: "70",
                image: "images/beef-kebab.jpg"
            },
            {
                nameEn: "Chicken Kebab",
                nameAr: "كباب دجاج",
                descEn: "Grilled chicken skewers with garlic sauce",
                descAr: "أسياخ دجاج مشوية مع صلصة الثوم",
                price: "65",
                image: "images/chicken-kebab.jpg"
            },
            {
                nameEn: "Chicken Kabsa",
                nameAr: "كبسة دجاج",
                descEn: "Crispy chicken pieces with rice and vegetables",
                descAr: "قطع دجاج مقلية مع الأرز والخضروات",
                price: "60",
                image: "images/chicken-kabsa.jpg"
            },
            {
                nameEn: "Mixed Grill Platter",
                nameAr: "طبق مشاوي مشكلة",
                descEn: "Assortment of grilled meats with rice and vegetables",
                descAr: "تشكيلة من اللحوم المشوية مع الأرز والخضروات",
                price: "120",
                image: "images/mixed-grill-platter.jpg"
            },
            {
                nameEn: "Meat Makloubah",
                nameAr: "لحم مقلوبة",
                descEn: "Layered rice with tender lamb, fried eggplant, and potatoes, flipped for serving",
                descAr: "أرز مع لحم الضأن والباذنجان والبطاطس، يُقلب عند التقديم",
                price: "80",
                image: "images/lahm-maklouba.jpg"
            },
            {
                nameEn: "Lamb Chops",
                nameAr: "ريش لحم غنم",
                descEn: "Marinated lamb chops with mint sauce",
                descAr: "ريش ضأن متبلة مع صلصة النعناع",
                price: "95",
                image: "images/lamb-chops.jpg"
            },
            {
                nameEn: "Neapolitan Pizza",
                nameAr: "بيتزا نيبولية",
                descEn: "Pizza with fresh tomatoes, mozzarella, and olive oil",
                descAr: "بيتزا مع طماطم طازجة، موزاريلا، وزيت زيتون",
                price: "55",
                image: "images/neapolitan-pizza.jpg"
            },
            {
                nameEn: "Vegetable Pizza",
                nameAr: "بيتزا خضروات",
                descEn: "Pizza with fresh vegetables",
                descAr: "بيتزا مع خضروات طازجة",
                price: "50",
                image: "images/vegetable-pizza.jpg"
            },
            {
                nameEn: "Chicken Shawarma",
                nameAr: "شاورما دجاج",
                descEn: "Delicious chicken shawarma with your choice of vegetables and sauce",
                descAr: "شاورما دجاج لذيذة مع اختيارك من الخضروات والصلصة",
                price: "65",
                image: "images/chicken-shawarma.jpg"
            }
        ],
        salads: [
            {
                nameEn: "Tabbouleh",
                nameAr: "تبولة",
                descEn: "Fresh parsley salad with bulgur, tomatoes, and mint",
                descAr: "سلطة البقدونس الطازجة مع البرغل والطماطم والنعناع",
                price: "22",
                image: "images/tabbouleh.jpg"
            },
            {
                nameEn: "Caesar Salad",
                nameAr: "سلطة سيزر",
                descEn: "Crisp romaine lettuce with Caesar dressing and croutons",
                descAr: "خس روماني مقرمش مع تتبيلة السيزر والخبز المحمص",
                price: "28",
                image: "images/caesar-salad.jpg"
            },
            {
                nameEn: "Fattoush",
                nameAr: "فتوش",
                descEn: "Mixed greens with crispy bread and sumac dressing",
                descAr: "خضار مشكلة مع الخبز المقرمش وتتبيلة السماق",
                price: "25",
                image: "images/fattoush.jpg"
            },
            {
                nameEn: "Greek Salad",
                nameAr: "سلطة يونانية",
                descEn: "Fresh vegetables with feta cheese and olives",
                descAr: "خضروات طازجة مع جبن الفيتا والزيتون",
                price: "26",
                image: "images/greek-salad.jpg"
            }
        ],
        soups: [
            {
                nameEn: "Lentil Soup",
                nameAr: "شوربة عدس",
                descEn: "Traditional Middle Eastern lentil soup",
                descAr: "شوربة العدس الشرق أوسطية التقليدية",
                price: "18",
                image: "images/lentil-soup.jpg"
            },
            {
                nameEn: "Chicken Soup",
                nameAr: "شوربة دجاج",
                descEn: "Hearty chicken soup with vegetables",
                descAr: "شوربة دجاج مغذية مع الخضروات",
                price: "20",
                image: "images/chicken-soup.jpg"
            },
            {
                nameEn: "Mushroom Soup",
                nameAr: "شوربة فطر",
                descEn: "Creamy mushroom soup with herbs",
                descAr: "شوربة فطر كريمية مع الأعشاب",
                price: "22",
                image: "images/mushroom-soup.jpg"
            },
            {
                nameEn: "Tomato Soup",
                nameAr: "شوربة طماطم",
                descEn: "Rich tomato soup with fresh basil",
                descAr: "شوربة طماطم غنية مع الريحان الطازج",
                price: "16",
                image: "images/tomato-soup.jpg"
            },
            {
                nameEn: "Vegetable Soup",
                nameAr: "شوربة خضار",
                descEn: "A healthy mix of seasonal vegetables in a light broth",
                descAr: "مزيج صحي من الخضروات الموسمية في مرق خفيف",
                price: "18",
                image: "images/vegetable-soup.jpg"
            }
        ],
        desserts: [
            {
                nameEn: "Kunafa",
                nameAr: "كنافة",
                descEn: "Traditional sweet cheese pastry soaked in sweet syrup",
                descAr: "حلوى تقليدية من الجبن والعجين المغمور بالقطر الحلو",
                price: "30",
                image: "images/desserts/kunafa.jpg"
            },
            {
                nameEn: "Ice Cream",
                nameAr: "آيس كريم",
                descEn: "Smooth and creamy ice cream in a variety of flavors",
                descAr: "آيس كريم ناعم وكريمي بنكهات متنوعة",
                price: "22",
                image: "images/desserts/ice-cream.jpg"
            },
            {
                nameEn: "Mahalabiya",
                nameAr: "مهلبية",
                descEn: "A silky milk pudding flavored with rose or orange blossom water",
                descAr: "بودينغ الحليب الناعم بنكهات ماء الورد أو الزهر",
                price: "32",
                image: "images/desserts/mahalabiya.jpg"
            },
            {
                nameEn: "Tiramisu",
                nameAr: "تيراميسو",
                descEn: "Classic coffee-flavored dessert with creamy mascarpone",
                descAr: "تحلية تقليدية بنكهة القهوة مع كريمة الماسكاربوني",
                price: "35",
                image: "images/desserts/tiramisu.jpg"
            },
            {
                nameEn: "Ma'amoul",
                nameAr: "معمول",
                descEn: "Traditional shortbread cookies stuffed with dates or nuts",
                descAr: "كوكيز تقليدية محشوة بالتمر أو المكسرات",
                price: "20",
                image: "images/desserts/maamoul.jpg"
            },
            {
                nameEn: "Baklava",
                nameAr: "بقلاوة",
                descEn: "Layered pastry filled with chopped nuts and sweetened with syrup",
                descAr: "حلوى من طبقات العجين المحشوة بالمكسرات والمحلاة بالقطر",
                price: "25",
                image: "images/desserts/baklava.jpg"
            },
            {
                nameEn: "Cheesecake",
                nameAr: "تشيز كيك",
                descEn: "A velvety smooth cheesecake with a buttery biscuit base",
                descAr: "كعكة الجبن الناعمة المخملية مع قاعدة بسكويت زبدية",
                price: "35",
                image: "images/desserts/cheesecake.jpg"
            },
            {
                nameEn: "Umm Ali",
                nameAr: "أم علي",
                descEn: "A warm Egyptian bread pudding with milk, nuts, and coconut",
                descAr: "بودينغ خبز مصري دافئ مع الحليب والمكسرات وجوز الهند",
                price: "25",
                image: "images/desserts/umm ali.jpg"
            },
            {
                nameEn: "Qatayef",
                nameAr: "قطايف",
                descEn: "A sweet stuffed pancake filled with nuts or cream",
                descAr: "فطائر محشوة بالمكسرات أو القشطة",
                price: "28",
                image: "images/desserts/qatayef.jpg"
            }
        ],
        "cold-beverages": [
            {
                nameEn: "Fresh Orange Juice",
                nameAr: "عصير برتقال طازج",
                descEn: "Freshly squeezed orange juice",
                descAr: "عصير برتقال طازج معصور",
                price: "15",
                image: "images/orange-juice.jpg"
            },
            {
                nameEn: "Mango Juice",
                nameAr: "عصير مانجو",
                descEn: "Fresh mango juice",
                descAr: "عصير مانجو طازج",
                price: "16",
                image: "images/mango-juice.jpg"
            },
            {
                nameEn: "Guava Juice",
                nameAr: "عصير جوافة",
                descEn: "Fresh guava juice",
                descAr: "عصير جوافة طازج",
                price: "16",
                image: "images/guava-juice.jpg"
            },
            {
                nameEn: "Strawberry Smoothie",
                nameAr: "سموذي فراولة",
                descEn: "Creamy strawberry smoothie",
                descAr: "سموذي فراولة كريمي",
                price: "18",
                image: "images/strawberry-smoothie.jpg"
            },
            {
                nameEn: "Iced Coffee",
                nameAr: "قهوة مثلجة",
                descEn: "Cold brew coffee served over ice",
                descAr: "قهوة باردة تُقدم مع الثلج",
                price: "14",
                image: "images/iced-coffee.jpg"
            }
        ],
        "hot-beverages": [
            {
                nameEn: "Black Tea",
                nameAr: "الشاي الأسود",
                descEn: "Classic black tea served with sugar and lemon",
                descAr: "الشاي الأسود الكلاسيكي يقدم مع السكر والليمون",
                price: "14",
                image: "images/black-tea.jpg"
            },
            {
                nameEn: "Green Tea",
                nameAr: "الشاي الأخضر",
                descEn: "Refreshing green tea with a hint of mint",
                descAr: "شاي أخضر رائع مع طعم النعناع",
                price: "15",
                image: "images/green-tea.jpg"
            },
            {
                nameEn: "Turkish Coffee",
                nameAr: "القهوة التركية",
                descEn: "A rich and flavorful Turkish coffee with milk and sugar",
                descAr: "قهوة تركية رائعة مع الحليب والسكر",
                price: "17",
                image: "images/turkish-coffee.jpg"
            },
            {
                nameEn: "Hot Chocolate",
                nameAr: "شكولاته ساخنة",
                descEn: "Hot chocolate served with marshmallows and whipped cream",
                descAr: "شوكولاتة ساخنة تقدم مع أعشاب من الفصيلة الخبازية وكريمة مخفوقة",
                price: "18",
                image: "images/hot-chocolate.jpg"
            },
            {
                nameEn: "Arabic Coffee",
                nameAr: "قهوة عربية",
                descEn: "Traditional Arabic coffee brewed with cinnamon",
                descAr: "قهوة عربية تقليدية مطبوخة مع الهيل",
                price: "18",
                image: "images/arabic-coffee.jpg"
            }
        ],
        extras: [
            {
                nameEn: "Tahini",
                nameAr: "طحيني",
                descEn: "Sweet and flavorful sauce made from sesame seeds",
                descAr: "صوص ناعم ولذيذ من بذور السمسم",
                price: "1.5",
                image: "images/tahini.jpg"
            },
            {
                nameEn: "Mayonnaise",
                nameAr: "مايونيز",
                descEn: "Mayonnaise with garlic",
                descAr: "مايونيز بالثوم",
                price: "2",
                image: "images/mayonnaise.jpg"
            },
            {
                nameEn: "Ketchup",
                nameAr: "كيتشوب",
                descEn: "Classic tomato-based sauce with a hint of sweetness, perfect with fries and grills",
                descAr: "صلصة طماطم كلاسيكية بطعم حلو خفيف، مثالية مع البطاطس والمشويات",
                price: "2",
                image: "images/ketchup.jpg"
            },
            {
                nameEn: "Honey Mustard Sauce",
                nameAr: "صلصة العسل والمايونيز",
                descEn: "A sweet and tangy sauce made with honey and mustard — perfect for dipping",
                descAr: "صوص حلو ولاذع مصنوع من العسل والخردل، مثالي للغموس",
                price: "2",
                image: "images/honey-mustard-sauce.jpg"
            },
            {
                nameEn: "Cheese Sauce",
                nameAr: "صلصة الجبن",
                descEn: "Warm, creamy cheese sauce that adds a rich and savory touch to any meal",
                descAr: "صوص حلو ولاذع مصنوع من الجبن، مثالي للمشويات",
                price: "3",
                image: "images/cheese-sauce.jpg"
            },
            {
                nameEn: "BBQ Sauce",
                nameAr: "صوص الباربكيو",
                descEn: "Sweet and smoky barbecue sauce, a perfect match for grilled dishes",
                descAr: "صوص ناعم ولذيذ من بذور السمسم، محبّب في المطبخ الشرق أوسطي",
                price: "2.5",
                image: "images/bbq-sauce.jpg"
            }
        ]
    },

    // Admin Configuration
    admin: {
        emails: [
            '<EMAIL>',
            '<EMAIL>'
        ]
    },

    // Save configuration to localStorage
    save: function() {
        localStorage.setItem('restaurantConfig', JSON.stringify(this));
        console.log('Configuration saved to localStorage');
    },

    // Load configuration from localStorage
    load: function() {
        const saved = localStorage.getItem('restaurantConfig');
        if (saved) {
            const config = JSON.parse(saved);
            // Merge saved config with current config
            Object.assign(this.restaurant, config.restaurant || {});
            Object.assign(this.menu, config.menu || {});
            Object.assign(this.admin, config.admin || {});
            console.log('Configuration loaded from localStorage');
        }
        return this;
    },

    // Update restaurant info
    updateRestaurant: function(newInfo) {
        Object.assign(this.restaurant, newInfo);
        this.save();
        this.updateUI();
    },

    // Add menu item
    addMenuItem: function(category, item) {
        if (!this.menu[category]) {
            this.menu[category] = [];
        }
        this.menu[category].push(item);
        this.save();
        this.updateUI();
    },

    // Update menu item
    updateMenuItem: function(category, index, item) {
        if (this.menu[category] && this.menu[category][index]) {
            this.menu[category][index] = item;
            this.save();
            this.updateUI();
        }
    },

    // Delete menu item
    deleteMenuItem: function(category, index) {
        if (this.menu[category] && this.menu[category][index]) {
            this.menu[category].splice(index, 1);
            this.save();
            this.updateUI();
        }
    },

    // Update UI elements
    updateUI: function() {
        // Update restaurant name in header if exists
        const headerNameEn = document.querySelector('.restaurant-name.en');
        const headerNameAr = document.querySelector('.restaurant-name.ar');
        if (headerNameEn) headerNameEn.textContent = this.restaurant.nameEn;
        if (headerNameAr) headerNameAr.textContent = this.restaurant.nameAr;

        // Update contact info if exists
        const phoneEl = document.querySelector('.contact-phone');
        if (phoneEl) phoneEl.textContent = this.restaurant.phone;

        // Trigger menu reload if function exists
        if (typeof loadMenuItems === 'function') {
            loadMenuItems();
        }
        if (typeof displayMenuItemsFromData === 'function') {
            displayMenuItemsFromData(this.menu);
        }
    },

    // Reset to default configuration
    reset: function() {
        localStorage.removeItem('restaurantConfig');
        localStorage.removeItem('menuItems');
        localStorage.removeItem('settings');
        location.reload();
    }
};

// Initialize configuration on load
window.RestaurantConfig.load();

// Force update if this is a new version
if (!localStorage.getItem('configVersion') || localStorage.getItem('configVersion') !== '2.2') {
    console.log('Updating to new configuration version 2.2...');
    localStorage.removeItem('restaurantConfig');
    localStorage.removeItem('menuItems');
    localStorage.setItem('configVersion', '2.2');
    window.RestaurantConfig.save();
}
