<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مطعم الأندلس | Al-Andalus Restaurant</title>
    <link rel="icon" href="images/favicon.png" type="image/png">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="language-switcher">
        <button onclick="switchLanguage('en')" id="en-btn">English</button>
        <button onclick="switchLanguage('ar')" class="active" id="ar-btn">العربية</button>
    </div>

    <div class="admin-login-btn">
        <button onclick="showLoginModal()" id="admin-login-btn">
            <i class="fas fa-user-shield"></i>
            <span class="en">Admin Login</span>
            <span class="ar">دخول المدير</span>
        </button>
    </div>
    
    <header>
        <div class="container">
            <div class="logo">
                <h1 class="en">Al-Andalus Restaurant</h1>
                <h1 class="ar">مطعم الأندلس</h1>
            </div>
            <nav>
                <ul>
                    <li><a href="#menu" class="en">Menu</a><a href="#menu" class="ar">القائمة</a></li>
                    <li><a href="#about" class="en">About</a><a href="#about" class="ar">عن المطعم</a></li>
                    <li><a href="#contact" class="en">Contact</a><a href="#contact" class="ar">اتصل بنا</a></li>
                </ul>
            </nav>
        </div>
    </header>
    
    <section id="hero">
        <div class="container">
            <h2 class="en">Authentic Middle Eastern Cuisine</h2>
            <h2 class="ar">مأكولات شرق أوسطية أصيلة</h2>
            <div class="button-container">
                <a href="#menu" class="btn en">View Menu</a>
                <a href="#menu" class="btn ar">القائمة</a>
            </div>
        </div>
    </section>
    
    <section id="image-slider">
        <div class="container">
            <div class="food-gallery">
                <div class="food-item"><img src="images/slider section/food1.jpg" alt="Food image 1"></div>
                <div class="food-item"><img src="images/slider section/food2.jpg" alt="Food image 2"></div>
                <div class="food-item"><img src="images/slider section/food3.jpg" alt="Food image 3"></div>
                <div class="food-item"><img src="images/slider section/food4.jpg" alt="Food image 4"></div>
                <div class="food-item"><img src="images/slider section/food5.jpg" alt="Food image 5"></div>
                <div class="food-item"><img src="images/slider section/food6.jpg" alt="Food image 6"></div>
                <div class="food-item"><img src="images/slider section/food7.jpg" alt="Food image 7"></div>
                <div class="food-item"><img src="images/slider section/food8.jpg" alt="Food image 8"></div>
                <div class="food-item"><img src="images/slider section/food9.jpg" alt="Food image 9"></div>
                <div class="food-item"><img src="images/slider section/food10.jpg" alt="Food image 10"></div>
            </div>
        </div>
    </section>
    
    <section id="menu">
        <div class="container">
            <h2 class="en">Our Menu</h2>
            <h2 class="ar">قائمتنا</h2>
            
            <div class="menu-categories">
                <button class="menu-tab active" onclick="showCategory('appetizers')">
                    <span class="en">Appetizers</span>
                    <span class="ar">المقبلات</span>
                </button>
                <button class="menu-tab" onclick="showCategory('main')">
                    <span class="en">Main Courses</span>
                    <span class="ar">الأطباق الرئيسية</span>
                </button>
                <button class="menu-tab" onclick="showCategory('salads')">
                    <span class="en">Salads</span>
                    <span class="ar">السلطات</span>
                </button>
                <button class="menu-tab" onclick="showCategory('soups')">
                    <span class="en">Soups</span>
                    <span class="ar">الشوربات</span>
                </button>
                <button class="menu-tab" onclick="showCategory('desserts')">
                    <span class="en">Desserts</span>
                    <span class="ar">الحلويات</span>
                </button>
                <button class="menu-tab" onclick="showCategory('cold-beverages')">
                    <span class="en">Cold Beverages</span>
                    <span class="ar">المشروبات الباردة</span>
                </button>
                <button class="menu-tab" onclick="showCategory('hot-beverages')">
                    <span class="en">Hot Beverages</span>
                    <span class="ar">المشروبات الساخنة</span>
                </button>
                <button class="menu-tab" onclick="showCategory('extras')">
                    <span class="en">Extras</span>
                    <span class="ar">إضافية</span>
                </button>
            </div>
            
            <!-- Appetizers Menu Items -->
            <div class="menu-items appetizers">
                <!-- Items will be loaded from config.js -->
            </div>
            
            <!-- Main Courses Menu Items -->
            <div class="menu-items main" style="display: none;">
                <!-- Items will be loaded from config.js -->
            </div>
        <!-- Desserts Menu Items -->
            <div class="menu-items desserts" style="display: none;">
                <div class="menu-item">
                    <div class="menu-item-info">
                        <h3 class="en">Kunafa</h3>
                        <h3 class="ar">كنافة</h3>
                        <p class="en">Traditional sweet cheese pastry soaked in sweet syrup</p>
                        <p class="ar">حلوى تقليدية من الجبن والعجين المغمور </p>
                        <span class="price">30 SAR</span>
                    </div>
                    <div class="menu-item-image" style="background-image: url('images/kunafa.jpg')"></div>
                </div>
                
                <div class="menu-item">
                    <div class="menu-item-info">
                        <h3 class="en">Ice Cream</h3>
                        <h3 class="ar">آيس كريم</h3>
                        <p class="en">Smooth and creamy ice cream in a variety of flavors—perfect to cool off and satisfy your sweet tooth</p>
                        <p class="ar">آيس كريم ناعم وكريمي بنكهات متنوعة، مثالي للانتعاش وإرضاء رغبتك في الحلويات</p>
                        <span class="price">22 SAR</span>
                    </div>
                    <div class="menu-item-image" style="background-image: url('images/ice-cream.jpg')"></div>
                </div>
                
                <div class="menu-item">
                    <div class="menu-item-info">
                        <h3 class="en">Mahalabiya</h3>
                        <h3 class="ar">مهلبية</h3>
                        <p class="en">A silky milk pudding flavored with rose or orange blossom water, and topped with nuts or coconut</p>
                        <p class="ar">بودينغ الحليب الناعم بنكهات ماء الورد أو الزهر، مزين بالمكسرات أو جوز الهند</p>
                        <span class="price">32 SAR</span>
                    </div>
                    <div class="menu-item-image" style="background-image: url('images/mahalabiya.jpg')"></div>
                </div>
                
                <div class="menu-item">
                    <div class="menu-item-info">
                        <h3 class="en">Tiramisu</h3>
                        <h3 class="ar">تيراميسو</h3>
                        <p class="en">Classic coffee-flavored dessert with creamy mascarpone and a touch of cocoa</p>
                        <p class="ar">تحلية تقليدية بنكهة القهوة مع كريمة الماسكاربوني ولمسة من الكاكاو</p>
                        <span class="price">35 SAR</span>
                    </div>
                    <div class="menu-item-image" style="background-image: url('images/tiramisu.jpg')"></div>
                </div>
                
                <div class="menu-item">
                    <div class="menu-item-info">
                        <h3 class="en">Ma'amoul</h3>
                        <h3 class="ar">معمول</h3>
                        <p class="en">Traditional shortbread cookies stuffed with dates, pistachios, or walnuts—served on special occasions</p>
                        <p class="ar">كوكيز تقليدية محشوة بالتمر أو الفستق أو الجوز، تُقدَّم في المناسبات الخاصة</p>
                        <span class="price">20 SAR</span>
                    </div>
                    <div class="menu-item-image" style="background-image: url('images/maamoul.jpg')"></div>
                </div>
                
                <div class="menu-item">
                    <div class="menu-item-info">
                        <h3 class="en">Baklava</h3>
                        <h3 class="ar">بقلاوة</h3>
                        <p class="en">Layered pastry filled with chopped nuts and sweetened with syrup</p>
                        <p class="ar">حلوى من طبقات العجين المحشوة بالمكسرات والمحلاة بالقطر</p>
                        <span class="price">25 SAR</span>
                    </div>
                    <div class="menu-item-image" style="background-image: url('images/baklava.jpg')"></div>
                </div>
                
                <div class="menu-item">
                    <div class="menu-item-info">
                        <h3 class="en">Cheesecake</h3>
                        <h3 class="ar">تشيز كيك</h3>
                        <p class="en">A velvety smooth cheesecake with a buttery biscuit base, with your choice of topping</p>
                        <p class="ar">كعكة الجبن الناعمة المخملية مع قاعدة بسكويت زبدية، مع اختيارك من الطبقة العلوية</p>
                        <span class="price">35 SAR</span>
                    </div>
                    <div class="menu-item-image" style="background-image: url('images/cheesecake.jpg')"></div>
                </div>
                
                <div class="menu-item">
                    <div class="menu-item-info">
                        <h3 class="en">Umm Ali</h3>
                        <h3 class="ar">أم علي</h3>
                        <p class="en">A warm Egyptian bread pudding with milk, nuts, and coconut</p>
                        <p class="ar">بودينغ خبز مصري دافئ مع الحليب والمكسرات وجوز الهند</p>
                        <span class="price">25 SAR</span>
                    </div>
                    <div class="menu-item-image" style="background-image: url('images/umm ali.jpg')"></div>
                </div>
                
                <div class="menu-item">
                    <div class="menu-item-info">
                        <h3 class="en">Qatayef</h3>
                        <h3 class="ar">قطايف</h3>
                        <p class="en">A sweet stuffed pancake filled with nuts or cream, deep-fried and drizzled with sugar syrup</p>
                        <p class="ar">فطائر محشوة بالمكسرات أو القشطة، تُقلى وتُسقى بالقطر. من أشهر حلويات رمضان</p>
                        <span class="price">28 SAR</span>
                    </div>
                    <div class="menu-item-image" style="background-image: url('images/qatayef.jpg')"></div>
                </div>              
            </div>
        
        <!-- Cold Beverages Menu Items -->
            <div class="menu-items cold-beverages" style="display: none;">
                <!-- Items will be loaded from config.js -->
            </div>

            <!-- Hot Beverages Menu Items -->
            <div class="menu-items hot-beverages" style="display: none;">
                <!-- Items will be loaded from config.js -->
            </div>

            <!-- Salads Menu Items -->
           <div class="menu-items salads" style="display: none;">
                <!-- Items will be loaded from config.js -->
           </div>
            <!-- Soups Menu Items -->
            <div class="menu-items soups" style="display: none;">
                <!-- Items will be loaded from config.js -->
            </div>

            <!-- Extras Menu Items -->
            <div class="menu-items extras" style="display: none;">
                <!-- Items will be loaded from config.js -->
            </div>
    </section>

    <section id="about" class="bg-section">
        <div class="container">
            <h2 class="en">About Al-Andalus Restaurant</h2>
            <h2 class="ar">عن مطعم الأندلس</h2>
            
            <div class="about-content">
                <div class="about-text">
                    <p class="en">At Al-Andalus, we offer authentic Middle Eastern cuisine with a genuine flavor and heritage. Every dish is prepared with fresh ingredients, bold flavors, and timeless recipes passed down through generations.</p>
                    <p class="ar">في الأندلس، نُقدّم مأكولات شرق أوسطية أصيلة بنكهةٍ أصيلةٍ وأصالةٍ تراثية. كل طبقٍ مُحضّرٌ بمكوناتٍ طازجة، ونكهاتٍ جريئة، ووصفاتٍ خالدةٍ توارثتها الأجيال.</p>
                    
                    <p class="en">Whether you're craving a quick shawarma, a warm bowl of lentil soup, or a full grill platter, our goal is to make you feel right at home with every bite.</p>
                    <p class="ar">سواءً كنتم ترغبون في شاورما سريعة، أو طبقٍ دافئٍ من شوربة العدس، أو طبقٍ مشويٍّ شُر، هدفنا هو أن نُشعركم وكأنكم في منزلكم مع كل لقمة.</p>
                
                    <p class="en">Thank you for choosing Al Andalus — we're honored to share our table with you, and we hope every meal leaves you smiling and satisfied. Enjoy your meal!</p>
                    <p class="ar">شكراً لاختياركم مطعم الأندلس، يشرفنا أن نشارككم طاولتنا، ونأمل أن تترك كل وجبة ابتسامةً ورضا. طاولتكم شهية!</p>
                </div>
                <div class="about-image" style="background-image: url('images/Authentic-Middle-Eastern-Restaurant.jpg')"></div>
            </div>
        </div>
    </section>

    <section id="contact">
        <div class="container">
            <h2 class="en">Visit Us</h2>
            <h2 class="ar">زورونا</h2>
            
            <div class="contact-info">
                <div>
                    <i class="fas fa-phone"></i>
                    <p>+966 12 534 0000</p>
                </div>
                
                <div>
                    <i class="fas fa-map-marker-alt"></i>
                    <p class="en">Ibrahim El Khalil Street, Al Haram, Makkah 21955, Saudi Arabia</p>
                    <p class="ar">شارع إبراهيم الخليل، الحرم، مكة 21955، المملكة العربية السعودية</p>
                </div>
                
                <div class="map-btn-container">
                    <a href="https://www.google.com/maps/place/Al+Andalus+Restaurant/@21.4199308,39.8241373,17z/data=!3m1!4b1!4m6!3m5!1s0x15c205f1f6b6fea7:0xc0ebbc9a20f54c65!8m2!3d21.4199308!4d39.8241373!16s%2Fg%2F11h3g3lhx3?entry=ttu&g_ep=EgoyMDI1MDUyMS4wIKXMDSoJLDEwMjExNDUzSAFQAw%3D%3D" 
                       class="map-btn en" target="_blank">Find Us on Google Maps</a>
                    <a href="https://www.google.com/maps/place/Al+Andalus+Restaurant/@21.4199308,39.8241373,17z/data=!3m1!4b1!4m6!3m5!1s0x15c205f1f6b6fea7:0xc0ebbc9a20f54c65!8m2!3d21.4199308!4d39.8241373!16s%2Fg%2F11h3g3lhx3?entry=ttu&g_ep=EgoyMDI1MDUyMS4wIKXMDSoJLDEwMjExNDUzSAFQAw%3D%3D" 
                       class="map-btn ar" target="_blank">خرائط جوجل</a>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <p class="en">&copy; 2025 Al-Andalus Restaurant. All rights reserved.</p>
            <p class="ar">© 2025 مطعم الأندلس. جميع الحقوق محفوظة.</p>
            <div id="admin-edit-btn" class="admin-link" style="display: none;">
                <button onclick="window.location.href='admin/dashboard.html'" title="Edit Site / تعديل الموقع">
                    <i class="fas fa-edit"></i>
                </button>
            </div>
        </div>
    </footer>

    <!-- Login Modal -->
    <div id="login-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="en">Admin Login</h2>
                <h2 class="ar">دخول المدير</h2>
                <button class="close-modal" onclick="hideLoginModal()">&times;</button>
            </div>
            <form id="firebase-login-form">
                <div class="input-group">
                    <label for="admin-email">
                        <span class="en">Email</span>
                        <span class="ar">البريد الإلكتروني</span>
                    </label>
                    <input type="email" id="admin-email" required>
                </div>
                <div class="input-group">
                    <label for="admin-password">
                        <span class="en">Password</span>
                        <span class="ar">كلمة المرور</span>
                    </label>
                    <input type="password" id="admin-password" required>
                </div>
                <div class="error-message" id="login-error-message"></div>
                <button type="submit" class="login-btn">
                    <span class="en">Login</span>
                    <span class="ar">دخول</span>
                </button>
            </form>
        </div>
    </div>

    <!-- Firebase Configuration -->
    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAuth, signInWithEmailAndPassword, createUserWithEmailAndPassword, onAuthStateChanged, signOut } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";
        import { getFirestore, doc, getDoc, setDoc } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";
        import { getStorage } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-storage.js";

        const firebaseConfig = {
            apiKey: "AIzaSyB-LGT0oIM6Q4S496tsqRgUdY7atU0-aEA",
            authDomain: "restaurant-61a71.firebaseapp.com",
            projectId: "restaurant-61a71",
            storageBucket: "restaurant-61a71.firebasestorage.app",
            messagingSenderId: "797821083486",
            appId: "1:797821083486:web:9e625179dc84c5179f79cf"
        };

        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);
        const storage = getStorage(app);

        // Make Firebase services available globally
        window.firebaseDB = db;
        window.firebaseStorage = storage;
        window.firestoreFunctions = { doc, getDoc, setDoc };

        // Make auth available globally
        window.firebaseAuth = auth;
        window.signInWithEmailAndPassword = signInWithEmailAndPassword;
        window.createUserWithEmailAndPassword = createUserWithEmailAndPassword;
        window.signOut = signOut;

        // Temporary function to create admin account (for testing)
        window.createAdminAccount = async (email, password) => {
            try {
                const userCredential = await createUserWithEmailAndPassword(auth, email, password);
                console.log('Admin account created:', userCredential.user.email);
                alert('Admin account created successfully!');
                return userCredential.user;
            } catch (error) {
                console.error('Error creating admin account:', error);
                alert('Error creating admin account: ' + error.message);
            }
        };

        // Check authentication state with admin email whitelist
        onAuthStateChanged(auth, (user) => {
            // Define approved admin emails
            const adminEmails = [
                '<EMAIL>',
                '<EMAIL>'
            ];

            if (user) {
                // User is signed in
                console.log('User signed in:', user.email);

                // Check if user email is in admin whitelist
                const isAdmin = adminEmails.includes(user.email);
                console.log('Is admin:', isAdmin);

                if (isAdmin) {
                    // User is an approved admin
                    document.getElementById('admin-login-btn').style.display = 'none';
                    document.getElementById('admin-edit-btn').style.display = 'block';

                    // Store admin info for admin dashboard
                    sessionStorage.setItem('adminLoggedIn', 'true');
                    sessionStorage.setItem('adminEmail', user.email);
                    sessionStorage.setItem('isApprovedAdmin', 'true');
                } else {
                    // User is logged in but not an approved admin - allow them to stay logged in
                    console.log('User logged in but not an admin - hiding edit button');
                    document.getElementById('admin-login-btn').style.display = 'none';
                    document.getElementById('admin-edit-btn').style.display = 'none';

                    // Clear admin session storage but keep user logged in
                    sessionStorage.removeItem('adminLoggedIn');
                    sessionStorage.removeItem('adminEmail');
                    sessionStorage.removeItem('isApprovedAdmin');

                    // Don't sign out - just hide admin features
                }
            } else {
                // User is signed out
                console.log('User signed out');
                document.getElementById('admin-login-btn').style.display = 'block';
                document.getElementById('admin-edit-btn').style.display = 'none';

                // Clear session storage
                sessionStorage.removeItem('adminLoggedIn');
                sessionStorage.removeItem('adminEmail');
                sessionStorage.removeItem('isApprovedAdmin');
            }
        });
    </script>

    <script src="config.js"></script>
    <script src="script.js"></script>
</body>
</html>


